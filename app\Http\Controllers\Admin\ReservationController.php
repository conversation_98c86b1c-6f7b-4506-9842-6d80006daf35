<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\ReservationsImport;
use App\Models\Reservation;
use App\Models\Stage;
use App\Models\TypeStage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ReservationController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Reservations/Index', [
            'reservations' => Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
                ->whereHas('stage', function ($query) {
                    $query->whereDate('date_debut', '>=', now()->toDateString());
                })
                ->orderBy('date_reservation', 'desc')
                ->paginate(10),
            'stages' => Stage::with('lieu')->get(),
            'typeStages' => TypeStage::all(),
            'users' => User::where('role', 'client')->get(),
            'isArchive' => false
        ]);
    }

    public function archive()
    {
        return Inertia::render('Admin/Reservations/Archive', [
            'reservations' => Reservation::with(['stage.lieu.ville.departement', 'user', 'typeStage'])
                ->whereHas('stage', function ($query) {
                    $query->whereDate('date_debut', '<', now()->toDateString());
                })
                ->orderBy('date_reservation', 'desc')
                ->paginate(10),
            'stages' => Stage::with('lieu')->get(),
            'typeStages' => TypeStage::all(),
            'users' => User::where('role', 'client')->get(),
            'isArchive' => true
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'stage_id' => 'required|exists:stages,id',
            'user_id' => 'required|exists:users,id',
            'type_stage_id' => 'required|exists:type_stages,id',
            'date_reservation' => 'required|date',
            'statut' => 'required|in:confirmée,en attente,annulée',
            'date_infraction' => 'nullable|date',
            'heure_infraction' => 'nullable|date_format:H:i',
            'lieu_infraction' => 'nullable|string|max:255',
            'permis_recto' => 'nullable|string|max:255',
            'permis_verso' => 'nullable|string|max:255',
            'lettre_48n_recto' => 'nullable|string|max:255',
            'lettre_48n_verso' => 'nullable|string|max:255',
            'permis_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'permis_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'lettre_48n_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'lettre_48n_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
        ]);

        // Supprimer les champs de fichiers de $validated
        $data = collect($validated)->except(['permis_recto_file', 'permis_verso_file', 'lettre_48n_recto_file', 'lettre_48n_verso_file'])->toArray();

        // Gérer les fichiers téléchargés
        if ($request->hasFile('permis_recto_file')) {
            $path = $request->file('permis_recto_file')->store('permis', 'public');
            $data['permis_recto'] = $path;
        }

        if ($request->hasFile('permis_verso_file')) {
            $path = $request->file('permis_verso_file')->store('permis', 'public');
            $data['permis_verso'] = $path;
        }

        if ($request->hasFile('lettre_48n_recto_file')) {
            $path = $request->file('lettre_48n_recto_file')->store('lettres', 'public');
            $data['lettre_48n_recto'] = $path;
        }

        if ($request->hasFile('lettre_48n_verso_file')) {
            $path = $request->file('lettre_48n_verso_file')->store('lettres', 'public');
            $data['lettre_48n_verso'] = $path;
        }

        Reservation::create($data);

        return redirect()->back()->with('success', 'Réservation créée avec succès.');
    }

    public function update(Request $request, Reservation $reservation)
    {
        $validated = $request->validate([
            'stage_id' => 'required|exists:stages,id',
            'user_id' => 'required|exists:users,id',
            'type_stage_id' => 'required|exists:type_stages,id',
            'date_reservation' => 'required|date',
            'statut' => 'required|in:confirmée,en attente,annulée',
            'date_infraction' => 'nullable|date',
            'heure_infraction' => 'nullable|date_format:H:i',
            'lieu_infraction' => 'nullable|string|max:255',
            'permis_recto' => 'nullable|string|max:255',
            'permis_verso' => 'nullable|string|max:255',
            'lettre_48n_recto' => 'nullable|string|max:255',
            'lettre_48n_verso' => 'nullable|string|max:255',
            'permis_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'permis_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'lettre_48n_recto_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'lettre_48n_verso_file' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048',
        ]);

        // Supprimer les champs de fichiers de $validated
        $data = collect($validated)->except(['permis_recto_file', 'permis_verso_file', 'lettre_48n_recto_file', 'lettre_48n_verso_file'])->toArray();

        // Gérer les fichiers téléchargés
        if ($request->hasFile('permis_recto_file')) {
            $path = $request->file('permis_recto_file')->store('permis', 'public');
            $data['permis_recto'] = $path;
        }

        if ($request->hasFile('permis_verso_file')) {
            $path = $request->file('permis_verso_file')->store('permis', 'public');
            $data['permis_verso'] = $path;
        }

        if ($request->hasFile('lettre_48n_recto_file')) {
            $path = $request->file('lettre_48n_recto_file')->store('lettres', 'public');
            $data['lettre_48n_recto'] = $path;
        }

        if ($request->hasFile('lettre_48n_verso_file')) {
            $path = $request->file('lettre_48n_verso_file')->store('lettres', 'public');
            $data['lettre_48n_verso'] = $path;
        }

        $reservation->update($data);

        return redirect()->back()->with('success', 'Réservation mise à jour avec succès.');
    }

    public function destroy(Reservation $reservation)
    {
        $reservation->delete();
        return redirect()->back()->with('success', 'Réservation supprimée avec succès.');
    }

    /**
     * Importer des réservations depuis un fichier Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls',
        ], [
            'file.required' => 'Veuillez sélectionner un fichier Excel',
            'file.file' => 'Le fichier doit être un fichier valide',
            'file.mimes' => 'Le fichier doit être au format Excel (.xlsx ou .xls)',
        ]);

        try {
            // Enregistrer le fichier temporairement pour le déboguer si nécessaire
            $path = $request->file('file')->store('temp');
            Log::info('Fichier importé: ' . $path);

            // Importer les données
            $import = new ReservationsImport();
            Excel::import($import, $request->file('file'));

            // Récupérer les statistiques
            $newUsersCount = $import->getNewUsersCount();
            $existingUsersCount = $import->getExistingUsersCount();
            $reservationsCount = $import->getReservationsCount();

            // Message de succès avec les statistiques
            $message = "Importation réussie: $reservationsCount réservations importées, ";
            $message .= "$newUsersCount nouveaux utilisateurs créés, ";
            $message .= "$existingUsersCount utilisateurs existants trouvés.";

            return redirect()->back()->with('success', $message);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            // Erreurs de validation
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = "Ligne {$failure->row()}: {$failure->errors()[0]}";
            }

            return redirect()->back()->withErrors(['file' => $errors]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'importation des réservations: ' . $e->getMessage());
            return redirect()->back()->withErrors(['file' => 'Une erreur est survenue lors de l\'importation: ' . $e->getMessage()]);
        }
    }
}
