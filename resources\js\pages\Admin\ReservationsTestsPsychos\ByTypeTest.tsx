import { Head, router } from '@inertiajs/react';
import { TestPsycho, ReservationTestPsycho, TypeTestPsycho, User, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft } from 'lucide-react';

interface ByTypeTestProps {
    reservations: PaginatedData<ReservationTestPsycho>;
    typeTest: TypeTestPsycho;
    tests: TestPsycho[];
    typeTests: TypeTestPsycho[];
    users: User[];
}

export default function ByTypeTest({ reservations, typeTest, tests, typeTests, users }: ByTypeTestProps) {
    const columns = [
        { key: 'id', label: 'ID' },
        {
            key: 'user',
            label: 'Client',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as ReservationTestPsycho;
                return reservation.user ? (
                    <div className="flex flex-col">
                        <span className="font-medium">{reservation.user.prenom} {reservation.user.nom}</span>
                        <span className="text-xs text-gray-500">{reservation.user.email}</span>
                    </div>
                ) : (
                    ''
                );
            },
        },
        {
            key: 'test_psycho',
            label: 'Test psychotechnique',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as ReservationTestPsycho;
                return reservation.test_psycho ? (
                    <div className="flex flex-col">
                        <span className="font-medium">{reservation.test_psycho.reference}</span>
                        <span className="text-xs text-gray-500">
                            {reservation.test_psycho.lieu?.nom} - {format(parseISO(reservation.test_psycho.date), 'dd/MM/yyyy', { locale: fr })}
                        </span>
                    </div>
                ) : (
                    ''
                );
            },
        },
        {
            key: 'date_reservation',
            label: 'Date de réservation',
            render: (value: unknown) => {
                const date = value as string;
                return format(parseISO(date), 'dd/MM/yyyy HH:mm', { locale: fr });
            },
        },
        {
            key: 'statut',
            label: 'Statut',
            render: (value: unknown) => {
                const statut = value as string;
                const statusColors = {
                    'confirmée': 'bg-green-100 text-green-800',
                    'en attente': 'bg-yellow-100 text-yellow-800',
                    'annulée': 'bg-red-100 text-red-800',
                };
                return (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[statut as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
                        {statut}
                    </span>
                );
            },
        },
        {
            key: 'motif',
            label: 'Motif',
            render: (value: unknown) => {
                const motif = value as string;
                return motif ? (
                    <span className="text-sm">{motif}</span>
                ) : (
                    <span className="text-muted-foreground">-</span>
                );
            },
        },
    ];

    const handleEdit = (reservation: ReservationTestPsycho) => {
        // Navigate to the main reservations page with edit functionality
        router.visit(route('admin.reservations-tests-psychos.index'), {
            data: { edit: reservation.id },
            preserveState: true,
        });
    };

    const handleDelete = (reservation: ReservationTestPsycho) => {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette réservation ?')) {
            router.delete(route('admin.reservations-tests-psychos.destroy', reservation.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Types de tests psychotechniques',
            href: '/admin/types-tests-psychos',
        },
        {
            title: `Réservations - ${typeTest.nom}`,
            href: `/admin/types-tests-psychos/${typeTest.id}/reservations`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Réservations - ${typeTest.nom}`} />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="flex items-center gap-4 mb-6">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit(route('admin.types-tests-psychos.index'))}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Retour aux types de tests psychotechniques
                    </Button>
                    <div className="flex-1">
                        <h1 className="text-2xl font-bold">Réservations pour le type de test "{typeTest.nom}"</h1>
                        {typeTest.description && (
                            <p className="text-muted-foreground">{typeTest.description}</p>
                        )}
                    </div>
                </div>

                <DataTable
                    title={`Réservations (${reservations.total})`}
                    columns={columns}
                    data={reservations.data.map(reservation => ({
                        id: reservation.id,
                        user: reservation.user,
                        test_psycho: reservation.test_psycho,
                        date_reservation: reservation.date_reservation,
                        statut: reservation.statut,
                        motif: reservation.motif,
                    }))}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: reservations.links,
                        from: reservations.from,
                        to: reservations.to,
                        total: reservations.total
                    }}
                />
            </div>
        </AppLayout>
    );
}
