import { Head, router } from '@inertiajs/react';
import { Stage, Reservation, TypeStage, User, BreadcrumbItem, PaginatedData } from '@/types';
import DataTable from '@/components/DataTable';
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/app-layout';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { ArrowLeft } from 'lucide-react';

interface ByStageProps {
    reservations: PaginatedData<Reservation>;
    stage: Stage;
    stages: Stage[];
    typeStages: TypeStage[];
    users: User[];
}

export default function ByStage({ reservations, stage, stages, typeStages, users }: ByStageProps) {
    const columns = [
        { key: 'id', label: 'ID' },
        {
            key: 'user',
            label: 'Client',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as Reservation;
                return reservation.user ? (
                    <div className="flex flex-col">
                        <span className="font-medium">{reservation.user.prenom} {reservation.user.nom}</span>
                        <span className="text-xs text-gray-500">{reservation.user.email}</span>
                    </div>
                ) : (
                    ''
                );
            },
        },
        {
            key: 'type_stage',
            index: 'type_stage_id',
            label: 'Type de stage',
            render: (_value: unknown, row: Record<string, unknown>) => {
                const reservation = row as unknown as Reservation;
                return reservation.type_stage?.nom || '';
            },
        },
        {
            key: 'date_reservation',
            label: 'Date de réservation',
            render: (value: unknown) => {
                const date = value as string;
                return format(parseISO(date), 'dd/MM/yyyy HH:mm', { locale: fr });
            },
        },
        {
            key: 'statut',
            label: 'Statut',
            render: (value: unknown) => {
                const statut = value as string;
                const statusColors = {
                    'confirmée': 'bg-green-100 text-green-800',
                    'en attente': 'bg-yellow-100 text-yellow-800',
                    'annulée': 'bg-red-100 text-red-800',
                };
                return (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[statut as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}`}>
                        {statut}
                    </span>
                );
            },
        },
    ];

    const handleEdit = (reservation: Reservation) => {
        // Navigate to the main reservations page with edit functionality
        router.visit(route('admin.reservations.index'), {
            data: { edit: reservation.id },
            preserveState: true,
        });
    };

    const handleDelete = (reservation: Reservation) => {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette réservation ?')) {
            router.delete(route('admin.reservations.destroy', reservation.id));
        }
    };

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Stages',
            href: '/admin/stages',
        },
        {
            title: `Réservations - ${stage.reference}`,
            href: `/admin/stages/${stage.id}/reservations`,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Réservations - ${stage.reference}`} />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="flex items-center gap-4 mb-6">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.visit(route('admin.stages.index'))}
                        className="flex items-center gap-2"
                    >
                        <ArrowLeft className="h-4 w-4" />
                        Retour aux stages
                    </Button>
                    <div className="flex-1">
                        <h1 className="text-2xl font-bold">Réservations pour le stage {stage.reference}</h1>
                        <p className="text-muted-foreground">
                            {stage.lieu?.nom} - {stage.lieu?.ville?.nom} ({stage.lieu?.ville?.departement?.code})
                            <br />
                            {format(parseISO(stage.date_debut), 'dd/MM/yyyy', { locale: fr })} - {stage.prix}€
                        </p>
                    </div>
                </div>

                <DataTable
                    title={`Réservations (${reservations.total})`}
                    columns={columns}
                    data={reservations.data.map(reservation => ({
                        id: reservation.id,
                        user: reservation.user,
                        type_stage: reservation.type_stage,
                        type_stage_id: reservation.type_stage_id,
                        date_reservation: reservation.date_reservation,
                        statut: reservation.statut,
                    }))}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    pagination={{
                        links: reservations.links,
                        from: reservations.from,
                        to: reservations.to,
                        total: reservations.total
                    }}
                />
            </div>
        </AppLayout>
    );
}
